package com.goclouds.crm.platform.system.controller;

import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.system.domain.SysCompanyModule;
import com.goclouds.crm.platform.system.service.SysCompanyModuleService;
import com.goclouds.crm.platform.utils.SecurityUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/module")
public class SysCompanyModuleController {

    private final SysCompanyModuleService sysCompanyModuleService;

    /**
     * 获取公司所有的配置模块
     *
     * @return 配置的模块列表
     */
    @GetMapping(value = "/getAllCompanyModule")
    public AjaxResult<List<SysCompanyModule>> getAllCompanyModule() {
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        return AjaxResult.ok(sysCompanyModuleService.getAllCompanyModule(companyId));
    }
}
