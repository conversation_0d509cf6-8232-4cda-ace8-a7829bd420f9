package com.goclouds.crm.platform.aiagent.controller;

import com.goclouds.crm.platform.aiagent.domain.SysTtsVoiceDictionary;
import com.goclouds.crm.platform.aiagent.service.SysTtsVoiceDictionaryService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

/**
 * aiAgent语音音色设置
 */
@RestController
@RequestMapping("/aiagent/tts/voice")
@RequiredArgsConstructor
public class SysTtsVoiceDictionaryController {
    private final SysTtsVoiceDictionaryService sysTtsVoiceDictionaryService;

    /** 查询所有语言列表 */
    @GetMapping("/languages")
    public AjaxResult<List<Map<String, String>>> listLanguages() {
        return AjaxResult.ok(sysTtsVoiceDictionaryService.listAllLanguages());
    }

    /** 查询所有音色列表，可选locale参数 */
    @GetMapping("/voices")
    public AjaxResult<List<Map<String, Object>>> listVoices(@RequestParam(value = "locale", required = false) String locale) {
        return AjaxResult.ok(sysTtsVoiceDictionaryService.listAllVoices(locale));
    }

    /** 试听 */
    @GetMapping("/auditions")
    public AjaxResult<?> auditions(@RequestParam String voiceCode,@RequestParam String speed, @RequestParam String volume) {
        return AjaxResult.ok(sysTtsVoiceDictionaryService.auditions(voiceCode, speed, volume));
    }

    /**
     * 获取voiceCode获取信息。
     *
     * @param voiceCode 语音code
     * @return 语音信息
     */
    @GetMapping("/voiceByVoiceCode")
    public AjaxResult<SysTtsVoiceDictionary> voiceByVoiceCode(@RequestParam String voiceCode){
        return AjaxResult.ok(sysTtsVoiceDictionaryService.voiceByVoiceCode(voiceCode));
    }

} 