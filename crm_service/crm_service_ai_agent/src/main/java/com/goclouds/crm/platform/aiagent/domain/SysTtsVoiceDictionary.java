package com.goclouds.crm.platform.aiagent.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 多平台TTS音色数据字典（含声音复刻）
 */
@Data
@TableName("sys_tts_voice_dictionary")
public class SysTtsVoiceDictionary {
    @TableId
    private String voiceId;
    private String companyId;
    private String platform;
    private String locale;
    private String language;
    private String voiceCode;
    private String voiceName;
    private String voiceGender;
    private String voiceStyle;
    private String voiceType;
    private String clonedFrom;
    private Long clonedUserId;
    private BigDecimal voiceSpeed;
    private BigDecimal voiceVolume;
    private String sampleText;
    private Integer dataStatus;
    private Date createTime;
    private Date updateTime;
} 