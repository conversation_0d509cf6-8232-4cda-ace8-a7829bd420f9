package com.goclouds.crm.platform.aiagent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.aiagent.domain.SysTtsVoiceDictionary;
import com.goclouds.crm.platform.common.domain.AjaxResult;

import java.util.List;
import java.util.Map;

public interface SysTtsVoiceDictionaryService extends IService<SysTtsVoiceDictionary> {
    /**
     * 查询所有语言列表（去重）
     */
    List<Map<String, String>> listAllLanguages();

    /**
     * 查询所有音色列表（可带语言参数，包含默认语速音量和语言）
     */
    List<Map<String, Object>> listAllVoices(String locale);

    /**
     * 试听
     */
    AjaxResult<?> auditions(String voiceCode, String speed, String volume);

    /**
     * 获取voiceCode获取信息。
     *
     * @param voiceCode 语音code
     * @return 语音信息
     */
    SysTtsVoiceDictionary voiceByVoiceCode(String voiceCode);


}