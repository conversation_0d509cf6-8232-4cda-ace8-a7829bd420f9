package com.goclouds.crm.platform.aiagent.controller;

import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import com.goclouds.crm.platform.aiagent.domain.vo.TranslateRequestVo;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingService;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 电话AI Agent通话设置
 * @description 电话AI Agent通话设置
 * @createTime 2025年07月10日
 */
@RestController
@RequestMapping("aiagent/call/setting")
@RequiredArgsConstructor
public class AiAgentCallSettingController {

    private final CrmAiAgentCallSettingService callSettingService;

    /**
     * 保存通话设置（支持新增和修改）
     * @param settingVo 通话设置信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public AjaxResult<Boolean> save(@RequestBody @Validated AiAgentCallSettingVo settingVo) {
        callSettingService.saveCallSetting(settingVo);
        return AjaxResult.ok();
    }

    /**
     * 获取通话设置详情
     * @param aiAgentId 智能体ID
     * @return 通话设置详情
     */
    @GetMapping("/detail")
    public AjaxResult<AiAgentCallSettingVo> detail(@RequestParam String aiAgentId) {
        AiAgentCallSettingVo vo = callSettingService.getCallSettingDetail(aiAgentId);
        return AjaxResult.ok(vo);
    }
    
    /**
     * AI智能翻译
     * @param requestVo 翻译请求参数，包含原语言、目标语言数组和内容
     * @return 翻译结果，包含目标语言和对应的翻译内容
     */
    @PostMapping("/translate")
    public AjaxResult<Map<String, String>> translate(@RequestBody @Validated TranslateRequestVo requestVo) {
        Map<String, String> translationResults = callSettingService.translateContent(
                requestVo.getSourceLanguage(), 
                requestVo.getTargetLanguages(), 
                requestVo.getContent(),
                requestVo.getPhraseType()
        );
        return AjaxResult.ok(translationResults);
    }
} 