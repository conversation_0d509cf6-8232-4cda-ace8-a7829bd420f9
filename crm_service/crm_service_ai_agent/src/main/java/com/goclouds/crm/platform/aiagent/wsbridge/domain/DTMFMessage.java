package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DTMF(按键)消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DTMFMessage extends BaseMessage {

    /**
     * 已检测到的 DTMF 数字
     */
    @JSONField(name = "detected_digits")
    private String detectedDigits;

    public DTMFMessage() {
        super("dtmf", null);
    }

    public DTMFMessage(String clientId, String reason) {
        super("dtmf", clientId);
        this.detectedDigits = detectedDigits;
    }
}
