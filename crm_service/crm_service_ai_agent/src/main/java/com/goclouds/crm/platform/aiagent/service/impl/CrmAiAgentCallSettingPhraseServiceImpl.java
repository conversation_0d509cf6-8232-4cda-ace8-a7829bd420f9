package com.goclouds.crm.platform.aiagent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentCallSettingPhrase;
import com.goclouds.crm.platform.aiagent.mapper.CrmAiAgentCallSettingPhraseMapper;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingPhraseService;
import org.springframework.stereotype.Service;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 电话AI Agent通话话术服务实现
 */
@Service
public class CrmAiAgentCallSettingPhraseServiceImpl extends ServiceImpl<CrmAiAgentCallSettingPhraseMapper, CrmAiAgentCallSettingPhrase> implements CrmAiAgentCallSettingPhraseService {

    @Override
    public List<CrmAiAgentCallSettingPhrase> getByAiAgentIdAndType(String aiAgentId, Integer phraseType) {
        LambdaQueryWrapper<CrmAiAgentCallSettingPhrase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAiAgentCallSettingPhrase::getAiAgentId, aiAgentId);
        if (phraseType != null) {
            queryWrapper.eq(CrmAiAgentCallSettingPhrase::getPhraseType, phraseType);
        }
        return this.list(queryWrapper);
    }

    @Override
    public List<CrmAiAgentCallSettingPhrase> getByAiAgentIdAndLanguage(String aiAgentId, String language) {
        LambdaQueryWrapper<CrmAiAgentCallSettingPhrase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAiAgentCallSettingPhrase::getAiAgentId, aiAgentId);
        if (StringUtils.isNotEmpty(language)) {
            queryWrapper.eq(CrmAiAgentCallSettingPhrase::getLanguage, language);
        }
        return this.list(queryWrapper);
    }
} 