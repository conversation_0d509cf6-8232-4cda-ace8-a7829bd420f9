package com.goclouds.crm.platform.aiagent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 电话AI Agent通话设置表（Key-Value结构）
 * @TableName crm_ai_agent_call_setting
 */
@TableName(value ="crm_ai_agent_call_setting")
@Data
public class CrmAiAgentCallSetting implements Serializable {
    /**
     * 通话设置项ID
     */
    @TableId
    private String settingId;

    /**
     * 智能体ID
     */
    private String aiAgentId;

    /**
     * 配置项名称
     */
    private String configKey;

    /**
     * 配置项的值
     */
    private String configValue;

    /**
     * 配置项说明
     */
    private String description;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 