package com.goclouds.crm.platform.aiagent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentCallSetting;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;

import java.util.List;
import java.util.Map;

/**
 * 电话AI Agent通话设置服务接口
 */
public interface CrmAiAgentCallSettingService extends IService<CrmAiAgentCallSetting> {

    /**
     * 根据智能体ID获取通话设置列表
     * 
     * @param aiAgentId 智能体ID
     * @return 通话设置列表
     */
    List<CrmAiAgentCallSetting> getByAiAgentId(String aiAgentId);
    
    /**
     * 保存通话设置（支持新增和修改）
     * 
     * @param settingVo 通话设置信息
     */
    void saveCallSetting(AiAgentCallSettingVo settingVo);
    
    /**
     * 获取通话设置详情
     * 
     * @param aiAgentId 智能体ID
     * @return 通话设置详情
     */
    AiAgentCallSettingVo getCallSettingDetail(String aiAgentId);

    /**
     * 翻译内容到多个目标语言
     *
     * @param sourceLanguage 原语言代码
     * @param targetLanguages 目标语言代码列表
     * @param content 要翻译的内容
     * @param phraseType 话术类型，1沉默时长话术类型，2结束话术类型
     * @return 翻译结果，key为目标语言代码，value为翻译后的内容
     */
    Map<String, String> translateContent(String sourceLanguage, String targetLanguages, String content, int phraseType);
} 