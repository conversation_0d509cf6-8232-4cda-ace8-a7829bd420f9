package com.goclouds.crm.platform.aiagent.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 电话AI Agent通话设置话术表
 * @TableName crm_ai_agent_call_setting_phrase
 */
@TableName(value ="crm_ai_agent_call_setting_phrase")
@Data
public class CrmAiAgentCallSettingPhrase implements Serializable {
    /**
     * 话术ID
     */
    @TableId
    private String phraseId;

    /**
     * 智能体ID
     */
    private String aiAgentId;

    /**
     * 话术类型（1:重复话术 2:沉默话术 3:结束话术）
     */
    private Integer phraseType;

    /**
     * 语言代码（如zh-CN）
     */
    private String language;

    /**
     * 话术内容
     */
    private String content;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
} 