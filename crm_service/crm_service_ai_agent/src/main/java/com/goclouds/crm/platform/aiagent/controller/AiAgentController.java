package com.goclouds.crm.platform.aiagent.controller;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.goclouds.crm.platform.aiagent.domain.AiAgentExecRequest;
import com.goclouds.crm.platform.aiagent.domain.AiAgentRes;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentInfo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentChannelCountVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentInfoVo;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentShareCodeVo;
import com.goclouds.crm.platform.aiagent.service.AiAgentService;
import com.goclouds.crm.platform.aiagent.utils.InvokeServiceUtil;
import com.goclouds.crm.platform.annotation.InnerAuth;
import com.goclouds.crm.platform.annotation.NoLogin;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.common.enums.AIGCModule;
import com.goclouds.crm.platform.common.utils.StringUtil;
import com.goclouds.crm.platform.controller.BaseController;

import cn.hutool.core.lang.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.http.MediaType;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


/**
 * 智能体Main
 */
@RestController
@RequestMapping
@Slf4j
public class AiAgentController extends BaseController {


    @Autowired
    private AiAgentService aiAgentService;


    /**
     * 智能体 执行
     *
     * @param aiAgentExecRequest
     * @return
     */
    @PostMapping(value = "/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @InnerAuth
//    @NoLogin
    public SseEmitter run(@RequestBody AiAgentExecRequest aiAgentExecRequest) {

        //AIGC计费校验
        InvokeServiceUtil instances = InvokeServiceUtil.getInstance();
        //如果返回false就不可调用
        log.info("调用 aiAgent run接口 AIGC计费校验开始 ");
        if(!instances.aigcRuestCheck(aiAgentExecRequest.getCompanyId(), AIGCModule.AI_Agent_OUTER.getCode())){
            log.info("调用 aiAgent AIGC计费校验失败 不可调用{}",aiAgentExecRequest.getCompanyId());
            return new SseEmitter();
        }
        
        SseEmitter emitter = new SseEmitter(120_000L); // 设置2分钟超时
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                // 先发个连接成功的结果，以防sse被断开
//                emitter.send(SseEmitter.event()
//                        .data(""));
                aiAgentService.run(aiAgentExecRequest, (data, sseFinish) -> {
                    try {
                        System.out.println("AI Agent Run SSE返回:" + sseFinish + data);
                        if(StringUtil.isNotEmpty(data)){
                            emitter.send(SseEmitter.event()
                                    .data(data));
                                    // .id(UUID.randomUUID().toString()));
                        }
                        if (sseFinish) {
                            emitter.complete();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


        return emitter;
    }

    /**
     * 智能体测试 执行
     * @param aiAgentExecRequest
     * @return
     */
//    @PostMapping("/run/test")
////    @NoLogin
//    public AjaxResult<AiAgentRes> runTest(@RequestBody AiAgentExecRequest aiAgentExecRequest) {
//        return aiAgentService.runTest(aiAgentExecRequest);
//    }


    /**
     * 智能体测试 执行 流式的
     * @param aiAgentExecRequest
     * @return
     */
    @PostMapping(value = "/run/test/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    @NoLogin
    public SseEmitter runTestStream(@RequestBody AiAgentExecRequest aiAgentExecRequest) {
        SseEmitter emitter = new SseEmitter(120_000L); // 设置2分钟超时
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                // 先发个连接成功的结果，以防sse被断开
//                emitter.send(SseEmitter.event()
//                        .data(""));
                aiAgentService.runTestStream(aiAgentExecRequest, (data, sseFinish) -> {
                    try {
                        log.info("SSE返回:" + sseFinish + data);
                        if(StringUtil.isNotEmpty(data)){
                            emitter.send(SseEmitter.event()
                                    .data(data));
                                    // .id(UUID.randomUUID().toString()));
                        }
                        if (sseFinish) {
                            emitter.complete();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


        return emitter;
    }

    /**
     * 智能体测试 执行 流式的 内部调的
     * @param aiAgentExecRequest
     * @return
     */
    @PostMapping(value = "/run/inner/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE, headers = "Content-Type=application/json")
    @InnerAuth
    public SseEmitter runInnerStream(@RequestBody AiAgentExecRequest aiAgentExecRequest) {
        SseEmitter emitter = new SseEmitter(30_000L); // 设置2分钟超时
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                aiAgentService.runTestStream(aiAgentExecRequest, (data, sseFinish) -> {
                    try {
                        System.out.println("发了:" + sseFinish + data);
                        if(StringUtil.isNotEmpty(data)){
                            emitter.send(SseEmitter.event()
                                    .data(data)
                                    .id(UUID.randomUUID().toString()));
                        }
                        if (sseFinish) {
                            emitter.complete();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });


        return emitter;
    }

    /**
     * 保存智能体信息
     *
     * @param agent 智能体参数
     * @return 智能体详情信息
     */
    @PostMapping("/saveAgentInfo")
    public AjaxResult<String> saveAgentInfo(@RequestBody @Validated AiAgentInfoVo agent) {
        return aiAgentService.saveAgentInfo(agent);
    }

    /**
     * 查询智能体详情
     *
     * @param aiAgentId 智能体查询参数
     * @return 智能体详情信息
     */
    @GetMapping("/details")
    public AjaxResult<AiAgentInfoVo> getAgentById(@RequestParam String aiAgentId) {
        return aiAgentService.getAgentById(aiAgentId);
    }

    /**
     * 分页查询智能体列表
     *
     * @param agentInfoVo 智能体查询参数
     * @return 智能体分页列表
     */
    @PostMapping("/pages")
    public AjaxResult<IPage<AiAgentInfoVo>> queryAgentInfoPages(@RequestBody(required = false) AiAgentInfoVo agentInfoVo) {
        return aiAgentService.queryAgentInfoPages(getPageParam(), agentInfoVo);
    }

    /**
     * 查询智能体数量
     *
     * @param
     * @return 响应信息
     */
    @PostMapping("/count")
    public AjaxResult<List<AiAgentChannelCountVo>> agentCount(@RequestBody(required = false) AiAgentInfoVo agentInfoVo) {
        return aiAgentService.agentCount(agentInfoVo);
    }

    /**
     * 删除智能体
     *
     * @param aiAgentId 智能体一标识ID
     * @return 响应信息
     */
    @PostMapping("/delete/{aiAgentId}")
    public AjaxResult<Boolean> deleteAgentById(@PathVariable String aiAgentId) {
        return aiAgentService.deleteAgentById(aiAgentId);
    }

    /**
     * 修改智能体状态
     *
     * @param aiAgentId 智能体一标识ID
     * @return 响应信息
     */
    @PostMapping("/updateStatus/{aiAgentId}")
    public AjaxResult<Boolean> updateStatus(@PathVariable String aiAgentId) {
        return aiAgentService.updateStatus(aiAgentId);
    }

    /**
     * 复制智能体
     *
     * @param aiAgentId 智能体一标识ID
     * @return 响应信息
     */
    @PostMapping("/copyAiagent/{aiAgentId}")
    public AjaxResult<AiAgentInfoVo> copyAiagent(@PathVariable String aiAgentId) {
        return aiAgentService.copyAiagent(aiAgentId);
    }

    /**
     * 新建默认智能体
     *
     */
    @GetMapping("/create/defaultAndEnsure")
    @InnerAuth
    public AjaxResult<AiAgentInfoVo> createDefaultAndEnsure(@RequestParam String companyId) {
        return aiAgentService.createDefaultAndEnsure(companyId);
    }

    /**
     * 修改智能体渠道
     * @param
     * @return
     */
    @PostMapping("/updateChannel/{aiAgentId}")
    public AjaxResult updateChannel(@PathVariable String aiAgentId, @RequestBody Map<String,String> map) {
        String s = map.get("channelIds");
        aiAgentService.updateChannel(aiAgentId,s);
        return AjaxResult.ok();
    }


    /**
     * 修改智能体名称
     * @param
     * @return
     */
    @PostMapping("/updateName/{aiAgentId}")
    public AjaxResult updateName(@PathVariable String aiAgentId, @RequestBody Map<String,String> map) {
        String name = map.get("aiAgentName");
        aiAgentService.updateName(aiAgentId, name);
        return AjaxResult.ok();
    }

    /**
     * 查询智能体数量
     * @param
     * @return 响应信息
     */
    @GetMapping("/queryAgentCount")
    @InnerAuth
    public AjaxResult<Integer> queryAgentCount(@RequestParam String companyId) {
        return aiAgentService.aiAgentCount(companyId);
    }

    /**
     * 智能体分享-获取分享码
     * @param
     * @return 响应信息
     */
    @GetMapping("/shareCodeInfo")
    public AjaxResult<AiAgentShareCodeVo> shareCodeInfo(@RequestParam String aiAgentId) {
        return aiAgentService.shareCodeInfo(aiAgentId);
    }

    /**
     * 根据分享码获取内容
     * @param shareCode 分享码
     * @return 分享内容
     */
    @GetMapping("/content/{shareCode}")
    public AjaxResult<JSONObject> getShareContent(@PathVariable String shareCode) {
        return aiAgentService.getShareContent(shareCode);
    }

    /**
     * 获取智能体信息，根据绑定的系统手机号查询。
     *
     * @param companyId   公司ID。
     * @param systemPhone 系统手机号。
     * @return 智能体信息。
     */
    @GetMapping("/getAgentInfoBySystemPhone")
    public AjaxResult<AiAgentInfoVo> getAgentInfoBySystemPhone(@RequestParam String companyId, @RequestParam String systemPhone) {
        return aiAgentService.getAgentInfoBySystemPhone(companyId, systemPhone);
    }

    /**
     * 获取智能体信息，根据智能体id。
     *
     * @param aiAgentId   智能体Id。
     * @return 智能体信息。
     */
    @GetMapping("/getAiAgentApiInfo")
    public AjaxResult<CrmAiAgentInfo> getAiAgentApiInfo(@RequestParam String aiAgentId) {
        return AjaxResult.ok(aiAgentService.getAiAgentApiInfo(aiAgentId));
    }

}
