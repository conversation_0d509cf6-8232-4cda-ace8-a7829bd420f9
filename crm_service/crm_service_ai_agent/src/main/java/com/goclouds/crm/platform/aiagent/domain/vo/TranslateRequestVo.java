package com.goclouds.crm.platform.aiagent.domain.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * AI智能翻译请求参数
 */
@Data
public class TranslateRequestVo implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 原语言代码（如zh-CN, en-US等）
     */
    @NotBlank(message = "原语言不能为空")
    private String sourceLanguage;
    
    /**
     * 目标语言代码列表
     */
    @NotEmpty(message = "目标语言不能为空")
    @Size(min = 1, message = "至少需要一种目标语言")
    private String targetLanguages;
    
    /**
     * 要翻译的内容
     */
    @NotBlank(message = "翻译内容不能为空")
    private String content;


    /**
     * 话术类型，1沉默时长话术类型，2结束话术类型
     */
    private int phraseType;
} 