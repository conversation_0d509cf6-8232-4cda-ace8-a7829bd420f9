package com.goclouds.crm.platform.aiagent.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.aiagent.domain.SysTtsVoiceDictionary;
import com.goclouds.crm.platform.aiagent.mapper.SysTtsVoiceDictionaryMapper;
import com.goclouds.crm.platform.aiagent.service.SysTtsVoiceDictionaryService;
import com.goclouds.crm.platform.aiagent.utils.AzureSpeechUtil;
import com.goclouds.crm.platform.common.constant.Constants;
import com.goclouds.crm.platform.common.domain.AjaxResult;
import com.goclouds.crm.platform.openfeignClient.client.system.CompanyTranscribeClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.system.SysCompanyTranscribeConfigVo;
import com.goclouds.crm.platform.utils.SecurityUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SysTtsVoiceDictionaryServiceImpl extends ServiceImpl<SysTtsVoiceDictionaryMapper, SysTtsVoiceDictionary> implements SysTtsVoiceDictionaryService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private CompanyTranscribeClient companyTranscribeClient;

    @Value("${azure.stt.endpointTemplate}")
    private String endpointTemplate; // = "https://%s.tts.speech.microsoft.com/cognitiveservices/v1";



    @Override
    public List<Map<String, String>> listAllLanguages() {
        List<SysTtsVoiceDictionary> all = this.list(new LambdaQueryWrapper<SysTtsVoiceDictionary>().eq(SysTtsVoiceDictionary::getDataStatus, 1));
        // 按locale+language去重
        return all.stream()
                .collect(Collectors.toMap(
                        v -> v.getLocale() + "|" + v.getLanguage(),
                        v -> v,
                        (v1, v2) -> v1))
                .values().stream()
                .map(v -> {
                    Map<String, String> map = new HashMap<>();
                    map.put("locale", v.getLocale());
                    map.put("language", v.getLanguage());
                    return map;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> listAllVoices(String locale) {
        LambdaQueryWrapper<SysTtsVoiceDictionary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysTtsVoiceDictionary::getDataStatus, 1);
        if (StringUtils.isNotBlank(locale)) {
            wrapper.eq(SysTtsVoiceDictionary::getLocale, locale);
        }
        List<SysTtsVoiceDictionary> all = this.list(wrapper);
        return all.stream().map(v -> {
            Map<String, Object> map = new HashMap<>();
            map.put("voiceId", v.getVoiceId());
            map.put("platform", v.getPlatform());
            map.put("locale", v.getLocale());
            map.put("language", v.getLanguage());
            map.put("voiceCode", v.getVoiceCode());
            map.put("voiceName", v.getVoiceName());
            map.put("voiceGender", v.getVoiceGender());
            map.put("voiceStyle", v.getVoiceStyle());
            map.put("voiceType", v.getVoiceType());
            map.put("clonedFrom", v.getClonedFrom());
            map.put("clonedUserId", v.getClonedUserId());
            map.put("voiceSpeed", v.getVoiceSpeed());
            map.put("voiceVolume", v.getVoiceVolume());
            map.put("sampleText", v.getSampleText());
            map.put("defaultVoiceSpeed", v.getVoiceSpeed());
            map.put("defaultVoiceVolume", v.getVoiceVolume());
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 试听
     * @param voiceCode 音色编码
     * @param speed 语速
     * @param volume 音量
     * @return 结果
     */
    @Override
    public AjaxResult<?> auditions(String voiceCode, String speed, String volume) {

        String redisKey = Constants.VOICE_AGENT_TTS + voiceCode;

        // 1. 查询Redis key中存储的音频字节码数据，如果存在则响应出去。
        String cachedAudioBase64 = redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isNotBlank(cachedAudioBase64)) {
            return AjaxResult.ok(cachedAudioBase64);
        }

        // 2. 如果Redis中不存在则查询音色数据。
        LambdaQueryWrapper<SysTtsVoiceDictionary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysTtsVoiceDictionary::getDataStatus, 1);
        if (StringUtils.isNotBlank(voiceCode)) {
            wrapper.eq(SysTtsVoiceDictionary::getVoiceCode, voiceCode);
        }
        SysTtsVoiceDictionary voiceDictionary = this.getOne(wrapper);
        if (null == voiceDictionary) {
            return AjaxResult.failure("音色信息异常");
        }

        // 3. 查询数据获取Azure TTS配置信息。
        String companyId = SecurityUtil.getLoginUser().getCompanyId(); // 公司id
        R<SysCompanyTranscribeConfigVo> transcribeConfig = companyTranscribeClient.getTranscribeConfig(companyId);
        if (null == transcribeConfig && null != transcribeConfig.getData()) {
            return AjaxResult.failure("获取Azure TTS配置信息异常");
        }
        SysCompanyTranscribeConfigVo data = transcribeConfig.getData();

        try {
            // 4. 调用Azure TTS服务进行转音频得到数据
            byte[] bytes = AzureSpeechUtil.synthesizeToBytes(data.getApiKey(), data.getPath(), endpointTemplate, voiceDictionary.getSampleText(),
                    voiceDictionary.getLocale(), voiceDictionary.getVoiceCode(), volume, speed);
            String audioBase64 = AzureSpeechUtil.byteToBase64(bytes);
            audioBase64 = "data:audio/mp3;base64," + audioBase64;
            // 5. 存储到Redis中
            redisTemplate.opsForValue().set(redisKey, audioBase64);

            // 6. 返回base64音频
            return AjaxResult.ok(audioBase64);
        } catch (Exception e) {
            return AjaxResult.failure("Azure TTS异常");
        }
    }

    /**
     * 获取voiceCode获取信息。
     *
     * @param voiceCode 语音code
     * @return 语音信息
     */
    public SysTtsVoiceDictionary voiceByVoiceCode(String voiceCode) {
        LambdaQueryWrapper<SysTtsVoiceDictionary> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysTtsVoiceDictionary::getDataStatus, 1);
        if (StringUtils.isNotBlank(voiceCode)) {
            wrapper.eq(SysTtsVoiceDictionary::getVoiceCode, voiceCode);
        }
        return this.getOne(wrapper);
    }
} 