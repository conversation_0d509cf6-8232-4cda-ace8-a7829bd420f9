# WebSocket API 完整文档

## 连接地址

```
ws://192.168.110.21:8765/voice-duplex/
```

---

## 1. 初始化连接 (init)

### 1.1 客户端建立连接

客户端通过WebSocket连接到服务器后，需要在URL中传递client_id参数：

```
ws://192.168.110.21:8765/voice-duplex/?client_id=1234567890
```

服务端返回：

```json
{
    "type": "init",
    "client_id": "1234567890",
    "status": "pending",
    "message": "Please initialize the connection"
}
```

### 1.2 客户端发送初始化请求

**消息类型**: `init`

```json
{
  "type": "init",
  "client_id": "1234567890",
  "company_id": "company_001",
  "aiAgentId": "agent_001",
  "settingMap": {
    "silence_detection_enabled": "1",
    "silence_threshold": "10",
    "silence_result_type": "1",
    "continuous_silence_count": "3",
    "silence_timeout_action": "silence"
  },
  "phraseList": [
    {
      "phraseId": "phrase_001",
      "phraseType": 1,
      "content": "您好，请问有什么可以帮助您的吗？",
      "language": "zh-CN"
    }
  ],
  "tts_language": "zh-CN",
  "tts_voice_name": "zh-CN-XiaoxiaoNeural",
  "language": "zh-CN",
  "s3_json_path": "s3://bucket/transcripts/client_12345_20250728_140000_transcript.json",
  "s3_wav_path": "s3://bucket/audio/client_12345_20250728_140000.wav",
  "audio_format": "mulaw",
  "audio_sample_rate": 8000
}
```

#### 参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|--|------|
| `type` | string | ✅ | 消息类型，固定值 "init" |
| `client_id` | string | ✅ | 客户端唯一标识符，由客户端生成并在URL中传递 |
| `company_id` | string | ✅ | 公司ID，用于加载公司配置 |
| `aiAgentId` | string | ✅ | AI代理ID |
| `settingMap` | object | ✅ | 静默检测配置对象 |
| `phraseList` | array | ✅ | 话术列表 |
| `tts_language` | string | ✅ | TTS语言设置 |
| `tts_voice_name` | string | ✅ | TTS声音名称 |
| `language` | string | ✅ | 语言设置 |
| `s3_json_path` | string | ✅ | S3转录JSON文件路径 |
| `s3_wav_path` | string | ✅ | S3录音WAV文件路径 |
| `audio_format` | string | ❌ | 客户端期望的音频格式，可选值: "pcm", "mulaw" |
| `audio_sample_rate` | number | ❌ | 客户端期望的音频采样率，如8000, 16000 |

#### settingMap 参数详解

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `silence_detection_enabled` | string | 是否启用静默检测 ("0"=禁用, "1"=启用) |
| `silence_threshold` | string | 静默阈值（秒数） |
| `silence_result_type` | string | 静默结果类型 ("1"=重新调用API, "2"=重复播放) |
| `continuous_silence_count` | string | 连续静默次数阈值 |
| `silence_timeout_action` | string | 静默超时动作，固定值 "silence" |

#### phraseList 参数详解

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `phraseId` | string | 话术唯一标识 |
| `phraseType` | number | 话术类型 (1=沉默话术, 2=结束话术) |
| `content` | string | 话术内容文本 |
| `language` | string | 话术语言代码 |

### 1.3 服务端响应

#### 1.3.1 连接成功

```json
{
  "type": "init",
  "client_id": "1234567890",
  "company_id": "company_001",
  "status": "success",
  "message": "Connection initialized successfully"
}
```

#### 1.3.2 连接失败

```json
{
  "type": "error",
  "client_id": "1234567890",
  "status": "error",
  "message": "缺少必需参数: company_id"
}
```

---

## 2. 心跳检测 (heartbeat)

### 2.1 客户端发送心跳

```json
{
  "type": "heartbeat",
  "client_id": "1234567890"
}
```

### 2.2 服务端响应

```json
{
  "type": "heartbeat",
  "client_id": "1234567890",
  "status": "success",
  "message": "pong"
}
```

---

## 3. 语音识别 (STT)

### 3.1 发送音频数据

```json
{
  "type": "stt_audio",
  "client_id": "1234567890",
  "audio_data": "base64编码的音频数据",
  "encoding": "mulaw",
  "sample_rate": 8000
}
```

#### 参数说明

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `type` | string | ✅ | 消息类型，固定值 "stt_audio" |
| `client_id` | string | ✅ | 客户端唯一标识符 |
| `audio_data` | string | ✅ | Base64编码的音频数据 |
| `encoding` | string | ❌ | 音频编码格式，可选值: "pcm", "mulaw"，默认为"pcm" |
| `sample_rate` | number | ❌ | 音频采样率，如8000或16000，默认为16000 |

**注意**:
- STT会话在init连接时自动初始化，无需单独启动和停止
- AI代理配置（aiAgentId、settingMap、phraseList）在init消息中一次性发送
- 连接成功后可直接发送音频数据，服务端会自动处理识别
- 当使用μ-law编码时，建议采样率设置为8000Hz
- 当使用PCM编码时，建议采样率设置为16000Hz

### 3.2 语音识别结果

```json
{
  "type": "stt_result",
  "client_id": "1234567890",
  "is_final": false,
  "text": "你好",
  "timestamp": 1640995200.123
}
```

#### 参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `type` | string | 消息类型，固定值 "stt_result" |
| `client_id` | string | 客户端唯一标识符 |
| `is_final` | boolean | 是否为最终识别结果 |
| `text` | string | 识别出的文本内容 |
| `timestamp` | number | 时间戳 |
| `new_msg_id` | string | 新消息ID（仅在发生打断时包含） |

---

## 4. AI回复消息

### 4.1 AI文本回复

```json
{
  "type": "api_response",
  "client_id": "1234567890",
  "msg_id": "msg_abc123",
  "session_id": "session_xyz789",
  "text": "您好！我是AI助手，很高兴为您服务。",
  "is_silence_reminder": false,
  "timestamp": 1640995200.123
}
```

#### 参数说明

| 参数名 | 类型 | 必选 | 说明 |
|--------|------|------|------|
| `type` | string | 是 | 消息类型，固定值 "api_response" |
| `client_id` | string | 是 | 客户端唯一标识符 |
| `msg_id` | string | 是 | 消息唯一标识符，静默提醒消息的msg_id以"silence_"开头 |
| `session_id` | string | 是 | 会话唯一标识符 |
| `text` | string | 是 | AI回复的文本内容 |
| `is_silence_reminder` | boolean | 是 | 是否为静默提醒消息，静默提醒时为true |
| `timestamp` | number | 是 | 时间戳 |

注意：静默提醒会同时发送文本消息（api_response类型）和音频消息（tts_audio类型），两者共用同一个msg_id，都标记is_silence_reminder为true。

### 4.2 AI回复完成

```json
{
  "type": "api_response",
  "client_id": "1234567890", 
  "msg_id": "msg_abc123",
  "session_id": "session_xyz789",
  "text": "您好！我是AI助手，很高兴为您服务。请问有什么可以帮助您的吗？",
  "is_silence_reminder": false,
  "timestamp": 1640995200.123
}
```

注意：
1. 当前系统不使用is_final字段来标识最终回复，所有API响应消息格式相同。
2. 静默提醒的文本消息格式与普通API响应消息相同，区别在于：
   - msg_id以"silence_"开头
   - is_silence_reminder字段为true
   - 文本内容通常是提示用户继续对话的话术

---

## 5. TTS音频事件

### 5.1 TTS音频数据

```json
{
  "type": "tts_audio",
  "client_id": "1234567890",
  "msg_id": "msg_abc123",
  "session_id": "session_xyz789",
  "format": "mulaw",
  "sample_rate": 8000,
  "audio": "base64编码的音频数据",
  "duration": 1.5,
  "is_silence_reminder": false
}
```

#### 参数说明

| 参数名 | 类型 | 必选 | 说明 |
|--------|------|------|------|
| `type` | string | 是 | 消息类型，固定值 "tts_audio" |
| `client_id` | string | 是 | 客户端唯一标识符 |
| `msg_id` | string | 是 | 消息唯一标识符，静默提醒消息的msg_id以"silence_"开头 |
| `session_id` | string | 是 | 会话唯一标识符 |
| `format` | string | 是 | 音频格式，可能值: "pcm", "mulaw" |
| `sample_rate` | number | 是 | 音频采样率，如8000或16000 |
| `audio` | string | 是 | Base64编码的音频数据 |
| `duration` | number | 是 | 音频时长（秒） |
| `is_silence_reminder` | boolean | 是 | 是否为静默提醒，静默提醒时为true |

### 5.2 TTS音频完成

```json
{
  "type": "tts_audio",
  "client_id": "1234567890",
  "msg_id": "msg_abc123",
  "session_id": "session_xyz789",
  "format": "mulaw",
  "sample_rate": 8000,
  "audio": "base64编码的音频数据",
  "duration": 0.8,
  "is_silence_reminder": false
}
```

注意：
1. 当前系统不使用sequence和is_final字段来标识音频片段顺序，而是将每个音频片段作为独立的消息发送。
2. 静默提醒的音频消息与普通TTS音频消息格式相同，区别在于：
   - msg_id以"silence_"开头
   - is_silence_reminder字段为true

---

## 6. 打断事件

### 6.1 TTS打断信号

```json
{
  "type": "tts_interrupt",
  "client_id": "1234567890",
  "interrupted_msg_id": "msg_abc123",
  "new_msg_id": "msg_def456",
  "reason": "user_speech_detected"
}
```

#### 参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `type` | string | 消息类型，固定值 "tts_interrupt" |
| `client_id` | string | 客户端唯一标识符 |
| `interrupted_msg_id` | string | 被打断的消息ID |
| `new_msg_id` | string | 新的消息ID |
| `reason` | string | 打断原因 |

---

## 7. 静默超时事件

### 7.1 静默超时通知

```json
{
  "type": "silence_timeout",
  "client_id": "1234567890",
  "message": "用户连续3次未响应，已达到最大静默次数(3)，建议断开连接",
  "silence_count": 3,
  "max_silence_count": 3,
  "timestamp": 1640995200.123,
  "action": "disconnect_recommended"
}
```

#### 参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `type` | string | 消息类型，固定值 "silence_timeout" |
| `client_id` | string | 客户端唯一标识符 |
| `message` | string | 描述性消息 |
| `silence_count` | number | 当前连续静默次数 |
| `max_silence_count` | number | 最大允许的静默次数 |
| `timestamp` | number | 时间戳 |
| `action` | string | 执行的动作类型，如 "disconnect_recommended" |

---

## 8. 错误事件

### 8.1 通用错误

```json
{
  "type": "error",
  "client_id": "1234567890",
  "status": "error",
  "message": "API请求失败，状态码: 502，已重试3次",
  "error_type": "api_failure"
}
```

#### 参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `type` | string | 消息类型，固定值 "error" |
| `client_id` | string | 客户端唯一标识符 |
| `status` | string | 状态，固定值 "error" |
| `message` | string | 错误描述信息 |
| `error_type` | string | 错误类型 (api_failure, service_failure) |

---

## 9. 关闭连接

### 9.1 客户端主动关闭

```json
{
  "type": "close",
  "client_id": "1234567890",
  "reason": "user_disconnect"
}
```

### 9.2 服务端关闭响应

```json
{
  "type": "close",
  "client_id": "1234567890",
  "status": "success",
  "message": "Connection closed successfully"
}
```

---

## 10. 音频格式规范

### 10.1 输入音频格式 (STT)

支持两种音频格式：

#### PCM格式
- **编码**: PCM
- **采样率**: 16kHz
- **位深**: 16bit
- **声道**: 单声道 (Mono)
- **传输格式**: Base64编码

#### μ-law格式
- **编码**: μ-law
- **采样率**: 8kHz
- **位深**: 8bit
- **声道**: 单声道 (Mono)
- **传输格式**: Base64编码

### 10.2 输出音频格式 (TTS)

服务器会根据客户端在初始化时指定的`audio_format`和`audio_sample_rate`返回相应格式的音频：

#### PCM格式
- **编码**: PCM
- **采样率**: 16kHz (可配置)
- **位深**: 16bit
- **声道**: 单声道 (Mono)
- **传输格式**: Base64编码

#### μ-law格式
- **编码**: μ-law
- **采样率**: 8kHz
- **位深**: 8bit
- **声道**: 单声道 (Mono)
- **传输格式**: Base64编码

---

## 11. 状态码说明

| 状态 | 说明 |
|------|------|
| `success` | 操作成功 |
| `error` | 操作失败 |
| `processing` | 正在处理中 |
| `pending` | 等待进一步操作 |

---

## 12. 错误类型说明

| 错误类型 | 说明 |
|----------|------|
| `api_failure` | API调用失败 |
| `service_failure` | 服务内部错误 |
| `invalid_params` | 参数无效 |
| `connection_error` | 连接错误 |
| `auth_error` | 认证错误 |

---

## 13. 消息队列格式

服务器将用户转录和AI回复保存到RabbitMQ队列中，使用以下格式：

### 13.1 用户转录消息

```json
{
  "workRecordId": "1234567890",
  "companyId": "company_001",
  "replyType": "2",
  "replyTime": "2023-07-28 14:00:00",
  "contentType": "1",
  "contentId": "uuid-generated-id",
  "content": "用户说的话"
}
```

### 13.2 AI回复消息

```json
{
  "workRecordId": "1234567890",
  "companyId": "company_001",
  "replyType": "3",
  "replyTime": "2023-07-28 14:00:05",
  "contentType": "1",
  "contentId": "uuid-generated-id",
  "content": "AI回复的内容"
}
```

#### 参数说明

| 参数名 | 类型 | 说明 |
|--------|------|------|
| `workRecordId` | string | 工作记录ID，与client_id相同 |
| `companyId` | string | 公司ID |
| `replyType` | string | 回复类型，"2"=客户，"3"=机器人 |
| `replyTime` | string | 回复时间，格式为"年-月-日 时:分:秒" |
| `contentType` | string | 内容类型，"1"=文本 |
| `contentId` | string | 内容ID，生成的UUID |
| `content` | string | 内容文本 |

---