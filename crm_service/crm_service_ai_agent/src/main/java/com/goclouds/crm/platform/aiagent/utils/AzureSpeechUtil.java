package com.goclouds.crm.platform.aiagent.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;

@Slf4j
public class AzureSpeechUtil {

    /**
     * 将文本语音合成并保存为文件
     */
    public static void synthesizeToFile(String speechKey, String speechRegion, String endpointTemplate,String msg,
                                        String language, String voiceCode, String volume,
                                        String speed, String outputFilePath) throws Exception {

        byte[] audioData = synthesizeToBytes(speechKey, speechRegion, endpointTemplate, msg, language, voiceCode, volume, speed);

        try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
            fos.write(audioData);
            System.out.println("✅ 音频文件保存成功: " + outputFilePath);
        }
    }

    /**
     * 将文本语音合成，返回字节数组（可用于存 Redis）
     */
    public static byte[] synthesizeToBytes(String speechKey, String speechRegion, String endpointTemplate, String msg,
                                           String language, String voiceCode, String volume,
                                           String speed) throws Exception {

        String endpoint = String.format(endpointTemplate, speechRegion);
        HttpURLConnection conn = null;

        try {
            URL url = new URL(endpoint);
            conn = (HttpURLConnection) url.openConnection();

            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Ocp-Apim-Subscription-Key", speechKey);
            conn.setRequestProperty("Content-Type", "application/ssml+xml");
            conn.setRequestProperty("X-Microsoft-OutputFormat", "audio-16khz-32kbitrate-mono-mp3");
            conn.setRequestProperty("User-Agent", "AzureTTSUtil-Java8");

            String ssml = buildSSML(msg, language, voiceCode, volume, speed);

            log.info("ssml: {}", ssml);

            try (OutputStream os = conn.getOutputStream()) {
                os.write(ssml.getBytes("UTF-8"));
            }

            int responseCode = conn.getResponseCode();
            if (responseCode == 200) {
                try (InputStream in = conn.getInputStream();
                     ByteArrayOutputStream out = new ByteArrayOutputStream()) {

                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }

                    return out.toByteArray();
                }
            } else {
                printError(conn);
                throw new RuntimeException("语音合成失败，HTTP响应码: " + responseCode);
            }
        } finally {
            if (conn != null) {
                conn.disconnect();
            }
        }
    }

    /**
     * byte 转 base64
     *
     * @param audioBytes 音频byte
     * @return base64
     */
    public static String byteToBase64(byte[] audioBytes) {
        return Base64.getEncoder().encodeToString(audioBytes);
    }

    /**
     * 构造SSML语音字符串
     */
    private static String buildSSML(String msg, String language, String voiceCode, String volume, String speed) {
        String a = "<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' "
                + "xmlns:mstts='http://www.w3.org/2001/mstts' xml:lang='" + language + "'>"
                + "<voice name='" + voiceCode + "'>"
                + "<prosody volume='" + volume + "' rate='" + speed + "'>"
                + msg
                + "</prosody>"
                + "</voice>"
                + "</speak>";
        log.info("a:", a);
        return a;
    }

    /**
     * 打印错误信息
     */
    private static void printError(HttpURLConnection conn) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getErrorStream(), "UTF-8"))) {
            String line;
            System.err.println("❌ 错误响应：");
            while ((line = reader.readLine()) != null) {
                System.err.println(line);
            }
        } catch (IOException e) {
            System.err.println("❌ 无法读取错误响应: " + e.getMessage());
        }
    }


}
