package com.goclouds.crm.platform.aiagent.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentCallSettingPhrase;

import java.util.List;

/**
 * 电话AI Agent通话话术服务接口
 */
public interface CrmAiAgentCallSettingPhraseService extends IService<CrmAiAgentCallSettingPhrase> {

    /**
     * 根据智能体ID和话术类型获取话术列表
     * 
     * @param aiAgentId 智能体ID
     * @param phraseType 话术类型（可为null，表示查询所有类型）
     * @return 话术列表
     */
    List<CrmAiAgentCallSettingPhrase> getByAiAgentIdAndType(String aiAgentId, Integer phraseType);
    
    /**
     * 根据智能体ID和语言获取话术列表
     * 
     * @param aiAgentId 智能体ID
     * @param language 语言代码
     * @return 话术列表
     */
    List<CrmAiAgentCallSettingPhrase> getByAiAgentIdAndLanguage(String aiAgentId, String language);
} 