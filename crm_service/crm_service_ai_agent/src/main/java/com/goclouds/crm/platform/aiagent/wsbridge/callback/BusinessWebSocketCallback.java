package com.goclouds.crm.platform.aiagent.wsbridge.callback;

import com.goclouds.crm.platform.aiagent.wsbridge.domain.ApiResponseMessage;
import com.goclouds.crm.platform.aiagent.wsbridge.domain.STTResultMessage;
import com.goclouds.crm.platform.aiagent.wsbridge.dto.BusinessConnectionRequest;

/**
 * 业务WebSocket回调接口
 * 定义WebSocket连接过程中的各种事件回调
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface BusinessWebSocketCallback {
    
    /**
     * 连接建立成功回调
     * 当WebSocket连接成功建立时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     */
    default void onConnected(BusinessConnectionRequest request, String connectionId) {
        // 默认空实现
    }
    
    /**
     * 连接断开回调
     * 当WebSocket连接断开时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param reason 断开原因
     */
    default void onDisconnected(BusinessConnectionRequest request, String connectionId, String reason) {
        // 默认空实现
    }
    
    /**
     * 连接错误回调
     * 当WebSocket连接发生错误时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param error 错误信息
     */
    default void onError(BusinessConnectionRequest request, String connectionId, String error) {
        // 默认空实现
    }
    
    /**
     * TTS音频数据回调
     * 当接收到TTS音频数据时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param audioData 音频数据（Base64编码）
     * @param format 音频格式
     * @param sampleRate 采样率
     * @param duration 时长（秒）
     */
    default void onTTSAudio(BusinessConnectionRequest request, String connectionId, 
                           String audioData, String format, Integer sampleRate, Double duration) {
        // 默认空实现
    }
    
    /**
     * TTS打断回调
     * 当TTS播放被打断时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param reason 打断原因
     */
    default void onTTSInterrupt(BusinessConnectionRequest request, String connectionId, String reason) {
        // 默认空实现
    }
    
    /**
     * 静默超时回调
     * 当用户静默超时时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param message 消息
     */
    default void onSilenceTimeout(BusinessConnectionRequest request, String connectionId,
                                 String message) {
        // 默认空实现
    }

    /**
     * 转人工回调
     * 当接收到to_agent数据时触发
     *
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     */
    default void onToAgent(BusinessConnectionRequest request, String connectionId,
                                 String message) {
        // 默认空实现
    }
    
    /**
     * ASR识别结果回调
     * 当接收到语音识别结果时触发
     * 
     * @param request 业务连接请求参数
     * @param message ASR识别结果
     */
    default void onASRResult(BusinessConnectionRequest request, STTResultMessage message) {
        // 默认空实现
    }
    /**
     * AI对话回复回调
     * 当接收到语音识别结果时触发
     *
     * @param request 业务连接请求参数
     * @param message AI回复结果
     */
    default void onApiResponse(BusinessConnectionRequest request, ApiResponseMessage message) {
        // 默认空实现
    }
    
    /**
     * 对话状态变更回调
     * 当对话状态发生变化时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param oldState 旧状态
     * @param newState 新状态
     */
    default void onConversationStateChanged(BusinessConnectionRequest request, String connectionId, 
                                          String oldState, String newState) {
        // 默认空实现
    }
    
    /**
     * 自定义消息回调
     * 当接收到自定义消息时触发
     * 
     * @param request 业务连接请求参数
     * @param connectionId 连接ID
     * @param messageType 消息类型
     * @param messageData 消息数据
     */
    default void onCustomMessage(BusinessConnectionRequest request, String connectionId, 
                                String messageType, Object messageData) {
        // 默认空实现
    }
}
