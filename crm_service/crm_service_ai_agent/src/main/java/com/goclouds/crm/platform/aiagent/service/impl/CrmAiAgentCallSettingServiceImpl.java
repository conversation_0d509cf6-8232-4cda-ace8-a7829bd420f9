package com.goclouds.crm.platform.aiagent.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentCallSetting;
import com.goclouds.crm.platform.aiagent.domain.CrmAiAgentCallSettingPhrase;
import com.goclouds.crm.platform.aiagent.domain.vo.AiAgentCallSettingVo;
import com.goclouds.crm.platform.aiagent.mapper.CrmAiAgentCallSettingMapper;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingPhraseService;
import com.goclouds.crm.platform.aiagent.service.CrmAiAgentCallSettingService;
import com.goclouds.crm.platform.common.utils.uuid.IdUtils;
import com.goclouds.crm.platform.openfeignClient.client.aigc.AigcChatClient;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.utils.SecurityUtil;

import org.apache.commons.lang3.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电话AI Agent通话设置服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CrmAiAgentCallSettingServiceImpl extends ServiceImpl<CrmAiAgentCallSettingMapper, CrmAiAgentCallSetting> implements CrmAiAgentCallSettingService {

    private final CrmAiAgentCallSettingPhraseService phraseService;
    private final AigcChatClient aigcChatClient;
    
    @Override
    public List<CrmAiAgentCallSetting> getByAiAgentId(String aiAgentId) {
        LambdaQueryWrapper<CrmAiAgentCallSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CrmAiAgentCallSetting::getAiAgentId, aiAgentId);
        return this.list(queryWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveCallSetting(AiAgentCallSettingVo settingVo) {
        if (settingVo == null || StringUtils.isEmpty(settingVo.getAiAgentId())) {
            throw new IllegalArgumentException("智能体ID不能为空");
        }
        
        String aiAgentId = settingVo.getAiAgentId();
        
        // 先删除该智能体的所有通话设置数据
        LambdaQueryWrapper<CrmAiAgentCallSetting> settingWrapper = new LambdaQueryWrapper<>();
        settingWrapper.eq(CrmAiAgentCallSetting::getAiAgentId, aiAgentId);
        this.remove(settingWrapper);
        
        // 删除该智能体的所有话术数据
        LambdaQueryWrapper<CrmAiAgentCallSettingPhrase> phraseWrapper = new LambdaQueryWrapper<>();
        phraseWrapper.eq(CrmAiAgentCallSettingPhrase::getAiAgentId, aiAgentId);
        phraseService.remove(phraseWrapper);
        
        // 处理配置项
        if (settingVo.getSettingMap() != null && !settingVo.getSettingMap().isEmpty()) {
            // 添加新配置
            List<CrmAiAgentCallSetting> settingList = new ArrayList<>();
            settingVo.getSettingMap().forEach((key, value) -> {
                CrmAiAgentCallSetting setting = new CrmAiAgentCallSetting();
                setting.setSettingId(IdUtils.fastSimpleUUID());
                setting.setAiAgentId(aiAgentId);
                setting.setConfigKey(key);
                setting.setConfigValue(value);
                
                // // 根据key设置description
                // switch (key) {
                //     case "allow_interrupt":
                //         setting.setDescription("是否允许客户打断（1允许，0不允许）");
                //         break;
                //     // 可以添加其他key的描述
                //     default:
                //         setting.setDescription(key);
                //         break;
                // }
                
                settingList.add(setting);
            });
            
            this.saveBatch(settingList);
        }
        
        // 处理话术
        if (settingVo.getPhraseList() != null && !settingVo.getPhraseList().isEmpty()) {
            List<CrmAiAgentCallSettingPhrase> phraseList = new ArrayList<>();
            
            for (AiAgentCallSettingVo.PhraseItem item : settingVo.getPhraseList()) {
                CrmAiAgentCallSettingPhrase phrase = new CrmAiAgentCallSettingPhrase();
                
                // 无论是否有phraseId，都生成新的ID
                phrase.setPhraseId(IdUtils.fastSimpleUUID());
                phrase.setAiAgentId(aiAgentId);
                phrase.setPhraseType(item.getPhraseType());
                phrase.setLanguage(item.getLanguage());
                phrase.setContent(item.getContent());
                
                phraseList.add(phrase);
            }
            
            // 批量保存话术
            phraseService.saveBatch(phraseList);
        }
    }
    
    @Override
    public AiAgentCallSettingVo getCallSettingDetail(String aiAgentId) {
        if (StringUtils.isEmpty(aiAgentId)) {
            throw new IllegalArgumentException("智能体ID不能为空");
        }
        
        AiAgentCallSettingVo vo = new AiAgentCallSettingVo();
        vo.setAiAgentId(aiAgentId);
        
        // 获取配置项
        List<CrmAiAgentCallSetting> settingList = this.getByAiAgentId(aiAgentId);
        if (settingList != null && !settingList.isEmpty()) {
            Map<String, String> settingMap = new HashMap<>();
            settingList.forEach(setting -> settingMap.put(setting.getConfigKey(), setting.getConfigValue()));
            vo.setSettingMap(settingMap);
        }
        
        // 获取话术列表
        LambdaQueryWrapper<CrmAiAgentCallSettingPhrase> phraseWrapper = new LambdaQueryWrapper<>();
        phraseWrapper.eq(CrmAiAgentCallSettingPhrase::getAiAgentId, aiAgentId);
        List<CrmAiAgentCallSettingPhrase> phraseList = phraseService.list(phraseWrapper);
        
        if (phraseList != null && !phraseList.isEmpty()) {
            List<AiAgentCallSettingVo.PhraseItem> phraseItems = phraseList.stream().map(phrase -> {
                AiAgentCallSettingVo.PhraseItem item = new AiAgentCallSettingVo.PhraseItem();
                item.setPhraseId(phrase.getPhraseId());
                item.setPhraseType(phrase.getPhraseType());
                item.setLanguage(phrase.getLanguage());
                item.setContent(phrase.getContent());
                return item;
            }).collect(Collectors.toList());
            
            vo.setPhraseList(phraseItems);
        }
        
        return vo;
    }
    
    @Override
    public Map<String, String> translateContent(String sourceLanguage, String targetLanguages, String content, int phraseType) {
        if (StringUtils.isBlank(content) || targetLanguages == null || targetLanguages.isEmpty()) {
            return new HashMap<>();
        }
        
        String companyId = SecurityUtil.getLoginUser().getCompanyId();
        log.info("开始翻译内容，公司ID：{}，原语言：{}，目标语言：{}，内容长度：{}", 
                companyId, sourceLanguage, targetLanguages, content);

        Map<String, String> results = new HashMap<>();
        
        try {
            // 调用AIGC服务进行翻译
            R<String> result = aigcChatClient.aiAgentCallSettingTranslate(companyId, sourceLanguage,
                    targetLanguages, content, phraseType);

            String answerJSONStr = result.getData();

            if (StringUtils.isBlank(answerJSONStr)) {
                log.error("翻译结果为空");
                return results;
            }

            String llmAnswerStr = extractJsonFromMD(answerJSONStr);
            log.info("LLM解析结果 - llmAnswerStr：{}", llmAnswerStr);

            if (StringUtils.isBlank(llmAnswerStr)) {
                log.error("解析JSON结果为空");
                return results;
            }

            JSONObject llmAnswer;
            try {
                llmAnswer = JSONObject.parseObject(llmAnswerStr);
                
                // 检查JSON结构是否符合预期
                if (llmAnswer.containsKey("translations")) {
                    // 获取translations数组并手动处理
                    JSONArray translationsArray = llmAnswer.getJSONArray("translations");
                    for (int i = 0; i < translationsArray.size(); i++) {
                        JSONObject translation = translationsArray.getJSONObject(i);
                        String targetLanguage = translation.getString("target_language");
                        String translatedContent = translation.getString("translated_content");
                        
                        if (StringUtils.isNotBlank(targetLanguage) && StringUtils.isNotBlank(translatedContent)) {
                            results.put(targetLanguage, translatedContent);
                        }
                    }
                    
                    log.info("翻译成功，目标语言数量：{}，成功翻译数量：{}", 
                            targetLanguages.split(",").length, results.size());
                } else {
                    log.error("翻译结果JSON格式不符合预期，缺少translations字段");
                }
            } catch (Exception e) {
                log.error("解析翻译结果JSON异常: {}", e.getMessage(), e);
            }
            
            return results;
        } catch (Exception e) {
            log.error("翻译异常，目标语言：{}，异常信息：{}", targetLanguages, e.getMessage(), e);
            return results;
        }
    }

    /**
     * 解析md格式的json数据，返回json字符串
     *
     * @param value md json
     * @return json string
     */
    public static String extractJsonFromMD(String value) {
        String jsonString = value.replaceAll("^```json\\s*", "").replaceAll("\\s*```$", "");
        if (jsonString.isEmpty()) {
            return "";
        }
        return jsonString;
    }
} 