package com.goclouds.crm.platform.aiagent.wsbridge.handler;

/**
 * WebSocket事件处理器接口
 * 定义WebSocket连接过程中的各种事件处理方法
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface WebSocketEventHandler {
    
    /**
     * 连接建立事件
     * 当WebSocket连接成功建立时触发
     * 
     * @param clientId 客户端ID
     */
    void onConnected(String clientId);
    
    /**
     * 消息接收事件
     * 当收到WebSocket消息时触发
     * 
     * @param message 接收到的消息内容
     */
    void onMessage(String message);
    
    /**
     * 连接断开事件
     * 当WebSocket连接断开时触发
     * 
     * @param clientId 客户端ID
     * @param code 断开代码
     * @param reason 断开原因
     */
    void onDisconnected(String clientId, int code, String reason);
    
    /**
     * 连接错误事件
     * 当WebSocket连接发生错误时触发
     * 
     * @param clientId 客户端ID
     * @param error 错误信息
     */
    void onError(String clientId, String error);
}
