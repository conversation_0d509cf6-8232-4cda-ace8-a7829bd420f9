package com.goclouds.crm.platform.aiagent.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 电话AI Agent通话设置VO
 */
@Data
public class AiAgentCallSettingVo implements Serializable {
    
    /**
     * 智能体ID
     */
    private String aiAgentId;
    
    /**
     * 配置项列表，key-value结构
     */
    private Map<String, String> settingMap;
    
    /**
     * 话术列表
     */
    private List<PhraseItem> phraseList;
    
    /**
     * 话术项
     */
    @Data
    public static class PhraseItem implements Serializable {
        /**
         * 话术ID，新增时可为空
         */
        private String phraseId;
        
        /**
         * 话术类型（1:沉默话术 2: 结束话术）
         */
        private Integer phraseType;
        
        /**
         * 语言代码（如zh-CN）
         */
        private String language;
        
        /**
         * 话术内容
         */
        private String content;
    }
} 