package com.goclouds.crm.platform.aiagent.wsbridge.domain;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * AI回复消息
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ApiResponseMessage extends BaseMessage {


    /**
     * 是否为最终识别结果
     */
    @JSONField(name = "is_final")
    private Boolean isFinal;

    /**
     * 消息唯一标识符
     * 静默提醒消息的msg_id以"silence_"开头
     */
    @JSONField(name = "msg_id")
    private String msgId;

    /**
     * 会话唯一标识符
     */
    @JSONField(name = "session_id")
    private String sessionId;

    /**
     * AI回复的文本内容
     */
    @JSONField(name = "text")
    private String text;

    /**
     * 是否为静默提醒消息
     * 静默提醒时为true
     */
    @JSONField(name = "is_silence_reminder")
    private Boolean isSilenceReminder;

    /**
     * 时间戳
     */
    @JSONField(name = "timestamp")
    private Double timestamp;

    public ApiResponseMessage() {
        super("api_response", null);
    }
}
