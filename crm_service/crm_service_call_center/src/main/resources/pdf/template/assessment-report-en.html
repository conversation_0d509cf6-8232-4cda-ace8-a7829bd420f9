<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <title>PDF Template Example</title>
    <style>
        @page {
            size: A4;
            margin: 0;
        }

        @page content {
            size: A4;
            margin-top: 20mm;
            margin-bottom: 15mm;
        }

        * {
            font-family: 'Microsoft YaHei', sans-serif !important;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 0;
        }

        .page {
            width: 210mm;
            height: 297mm;
            box-sizing: border-box;
            page-break-after: always;
            display: block;
            position: relative;
        }

        .cover-page {
            background-image: url('data:image/jpeg;base64,${homeBgBase64}');
            background-size: 100% 100%;
            text-align: center;
            display: block;
            padding-top: 100mm;
            position: relative;
        }
        .logo {
            position: absolute;
            top: 20px;
            left: 20px;
            max-width: 125px;
        }
        .cover-content {
            width: 100%;
        }

        .cover-page h1 {
            color: #333;
            text-align: center;
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", sans-serif;
            font-size: 40px;
            font-style: normal;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 15px;
            margin-top: 0;
        }

        .cover-page p {
            font-size: 18px;
            margin: 0 auto;
            margin-bottom: 5px;
            text-align: center;
            font-family: "Microsoft YaHei UI", "Microsoft YaHei", sans-serif;
            line-height: 1.4;
        }

        .content-page {
            height: auto;
            background-color: #ffffff;
            text-align: left;
            padding: 0 44px;
            box-sizing: border-box;
            page: content;
            counter-reset: page 0;
        }

        .content-page .page-score {
            margin-top: 20px;
            margin-bottom: 20px;
        }
        .content-page .content {
            padding-bottom: 30px;
        }

        .page-score {
            color: ${scoreColor} !important;
            width: 500px;
            margin: 20px auto 20px auto;
            text-align: center;
        }

        .page-score .score {
            color: ${scoreColor} !important;
            font-family: "Microsoft YaHei", sans-serif !important;
            font-size: 70px !important;
            font-style: normal !important;
            font-weight: 700 !important;
            line-height: 1em !important;
            display: inline-block !important;
            margin-right: 5px !important;
        }

        .page-score .score span {
            color: ${scoreColor} !important;
            font-family: "Microsoft YaHei", sans-serif !important;
            font-size: 20px !important;
            font-style: normal !important;
            font-weight: 700 !important;
            line-height: 1em !important;
        }

        .page-score .finalText {
            font-size: 14px !important;
            color: #333 !important;
            display: block !important;
            text-align: center;
            margin-top: 5px;
        }

        .content {
            width: 100%;
            box-sizing: border-box;
            margin-top: 0;
        }

        .content-page h3 {
            margin-top: 15px;
            margin-bottom: 8px;
            font-size: 18px;
        }

        .content p {
            margin: 3px 0;
            line-height: 1.4;
            font-size: 14px;
        }

        .end-page {
            background-image: url('data:image/jpeg;base64,${endPngBase64}');
            background-size: 100% 100%;
            text-align: center;
            display: block;
            padding-top: 100mm;
        }

        .end-page .content {
            padding: 20mm;
            box-sizing: border-box;
        }

        .end-page h2 {
            font-size: 28px;
        }

        .paragraph {
            margin-bottom: 12px;
            line-height: 1.6;
        }

        .content .stroke {
            stroke-width: 1px;
            stroke: rgba(52, 99, 252, 0.30);
        }

        .categories {
            width: 100%;
            margin-top: 15px;
        }

        .category {
            margin-bottom: 15px;
            border-bottom: 1px solid #E6E6E6;
            padding-bottom: 12px;
        }

        .one-level {
            margin: 0;
            padding-bottom: 14px;
            font-family: "Microsoft YaHei", sans-serif;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #E6E6E6;
            margin-bottom: 14px;
        }

        .sub-category {
            margin-bottom: 12px;
            padding-top: 4px;
        }

        .two-level {
            margin: 4px 0;
            padding-left: 10px;
            font-size: 14px !important;
            font-weight: bold;
        }

        .evaluation-item {
            margin-bottom: 6px;
        }

        .ai-evaluation,
        .normal-ai-evaluation,
        .artificial-evaluation {
            padding-left: 10px;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .ai-evaluation {
            text-decoration: line-through;
            color: #989898;
        }

        .normal-ai-evaluation {
            text-decoration: none;
            color: inherit;
        }

        .artificial-evaluation {
            padding-left: 10px;
            margin-bottom: 10px;
            font-size: 12px;
        }

        .subject {
            display: block;
            margin: 14px 0;
            font-weight: normal;
            font-size: 14px;
            color: #333;
            font-family: "Microsoft YaHei";
            font-style: normal;
            font-weight: bold;
        }

        .add {
            color: #3463FC;
            font-family: "Microsoft YaHei";
            font-style: normal;
            font-weight: 700;
            line-height: 1.5;
        }

        .addScore {
            color: #333;
            font-family: "Microsoft YaHei";
            font-style: normal;
            font-weight: 700;
            line-height: 1.5;
            text-decoration: underline;
        }

        .rule-reference,
        .rule-remark {
            max-width: 100%;
            box-sizing: border-box;
            padding: 8px 12px;
            margin: 6px 10px;
            border-radius: 4px;
            font-size: 13px;
            line-height: 1.4;
            border: 1px solid transparent;
        }

        .rule-reference {
            border-left-color: #999;
            background-color: #F9F9F9;
        }

        .rule-remark {
            border-left-color: #FFAE00;
            background-color: #FFFAF1;
        }

        .rule-point-title{
            color: #333;
            margin: 0 0 4px 0;
            font-weight: bold;
        }

        .rule-reference div:nth-child(2),
        .rule-remark div:nth-child(2) {
            color: #999;
            font-family: "Microsoft YaHei", sans-serif;
            font-style: normal;
            font-weight: 400;
            line-height: 1.4;
            margin: 0;
        }

        .ai-evaluation{
            margin-bottom: 14px;
        }

        .ai-evaluation img,
        .normal-ai-evaluation img,
        .artificial-evaluation img {
            width: 16px;
            height: 16px;
            vertical-align: middle;
            margin-right: 5px;
        }

        .create-time {
            color: #333;
            text-align: center;
            font-family: "Microsoft YaHei UI";
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            margin-top: 40px;
        }
    </style>
</head>

<body>
<div class="page cover-page">
    <img src="data:image/png;base64,${logoPngBase64}" alt="Logo" class="logo"/>
    <div class="cover-content">
        <h1>Intelligent Quality Inspection Report</h1>
        <p>Evaluation Time: <span th:text="${record.createTimeStr}"></span></p>
        <p>Evaluated Person: <span th:text="${record.assessedAgentName}"></span></p>
        <p>Evaluator: <span th:text="${record.assessorName}"></span></p>
    </div>
</div>

<div class="page content-page">
    <div class="page-score">
        <div class="score" th:text="${#numbers.formatInteger(record.totalScore, 0)}"><span>points</span></div>
        <div class="finalText">Final Evaluation Score</div>
    </div>

    <div class="content">
        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="1" viewBox="0 0 100 1"
             preserveAspectRatio="none" fill="none">
            <path d="M0 0.5 H100" stroke="rgba(230, 230, 230, 1)" stroke-linecap="round" />
        </svg>

        <h3>Overview</h3>
        <p>Assessment Name: <span th:text="${record.assessmentName}"></span></p>
        <p>Assessment Version: <span th:text="${record.versionNo}"></span></p>
        <p>Evaluation Time: <span th:text="${record.createTimeStr}"></span></p>
        <p>Evaluated Person: <span th:text="${record.assessedAgentName}"></span></p>
        <p>Evaluator: <span th:text="${record.assessorName}"></span></p>
        <svg style="margin: 20px 0;" xmlns="http://www.w3.org/2000/svg" width="100%" height="1" viewBox="0 0 100 1"
             preserveAspectRatio="none" fill="none">
            <path d="M0 0.5 H100" stroke="rgba(230, 230, 230, 1)" stroke-linecap="round" />
        </svg>

        <div class="categories">
            <div th:each="category : ${categories}">
                <div th:if="${category.categoryLevel == 1}" class="one-level">
                    <b th:text="${category.numberedCategoryName}"></b>
                </div>
                <div th:unless="${category.categoryLevel == 1}" class="two-level">
                    <b th:text="${category.numberedCategoryName}"></b>
                </div>

                <div th:each="rule : ${category.rules}" class="evaluation-item">
                    <span class="subject" th:text="${rule.ruleName}"></span>

                    <!-- Quality Checkpoints and AIGC Scoring Rules -->
                    <div th:if="${not #lists.isEmpty(rule.qualityCheckpoints) or not #strings.isEmpty(rule.aiAssessmentRule)}"
                         class="rule-reference rule-checkpoints-and-aigc">
                        <div class="rule-point-title">Quality Checkpoints: </div>
                        <div>
                            <div th:each="point, pointStat : ${rule.qualityCheckpoints}"
                                 th:text="${point.pointDesc}"
                                 th:style="'color:#999;'"></div>


                            <div th:if="${not #strings.isEmpty(rule.aiAssessmentRule)}">
                                <br/>
                                <b>AIGC Scoring Rules: </b>

                                <th:block
                                        th:with="ruleStr=${rule.aiAssessmentRule},
                 scoreAction=${record.scoringRule == 1 ? 'add' : 'deduct'}">

                                    <!-- Type 1: Any Checkpoint -->
                                    <th:block th:if="${#strings.contains(ruleStr, '&quot;type&quot;:&quot;1&quot;')}">
                                        For each occurrence of any checkpoint,
                                        <span th:text="${scoreAction}"></span>

                                        <!-- Points per occurrence -->
                                        <span th:if="${#strings.contains(ruleStr, 'deduct_per_time')}">
                <th:block th:with="idx=${#strings.indexOf(ruleStr, 'deduct_per_time') + 17}">
                    <span th:text="${#strings.substring(ruleStr, idx, idx+2).replaceAll('[^0-9]', '')}">1</span>
                </th:block>
            </span>
                                        <span th:unless="${#strings.contains(ruleStr, 'deduct_per_time')}">1</span> point(s), up to a maximum of
                                        <span th:text="${scoreAction}"></span>

                                        <!-- Maximum cumulative points -->
                                        <span th:if="${#strings.contains(ruleStr, 'max_deduct')}">
                <th:block th:with="idxMax=${#strings.indexOf(ruleStr, 'max_deduct') + 12}">
                    <span th:text="${#strings.substring(ruleStr, idxMax, idxMax+2).replaceAll('[^0-9]', '')}">1</span>
                </th:block>
            </span>
                                        <span th:unless="${#strings.contains(ruleStr, 'max_deduct')}">1</span> point(s)
                                    </th:block>

                                    <!-- Type 2: Multiple Checkpoints -->
                                    <th:block th:if="${#strings.contains(ruleStr, '&quot;type&quot;:&quot;2&quot;')}">
                                        For multiple checkpoints, when occurrences are

                                        <!-- Comparison operator -->
                                        <span th:if="${#strings.contains(ruleStr, '&quot;compare&quot;:&quot;&lt;&quot;')}">less than</span>
                                        <span th:if="${#strings.contains(ruleStr, '&quot;compare&quot;:&quot;&gt;&quot;')}">greater than</span>
                                        <span th:if="${#strings.contains(ruleStr, '&quot;compare&quot;:&quot;=&quot;')}">equal to</span>
                                        <span th:unless="${#strings.contains(ruleStr, '&quot;compare&quot;:&quot;&lt;&quot;') or
                             #strings.contains(ruleStr, '&quot;compare&quot;:&quot;&gt;&quot;') or
                             #strings.contains(ruleStr, '&quot;compare&quot;:&quot;=&quot;')}">
                meeting the condition
            </span>

                                        <!-- Times -->
                                        <span th:if="${#strings.contains(ruleStr, '&quot;times&quot;')}">
                <th:block th:with="idxTimes=${#strings.indexOf(ruleStr, '&quot;times&quot;') + 8}">
                    <span th:text="${#strings.substring(ruleStr, idxTimes, idxTimes+2).replaceAll('[^0-9]', '')}">1</span>
                </th:block>
            </span>
                                        <span th:unless="${#strings.contains(ruleStr, '&quot;times&quot;')}">1</span> time(s),
                                        <span th:text="${scoreAction}"></span>

                                        <!-- Points -->
                                        <span th:if="${#strings.contains(ruleStr, '&quot;deduct&quot;')}">
                <th:block th:with="idxD=${#strings.indexOf(ruleStr, '&quot;deduct&quot;') + 9}">
                    <span th:text="${#strings.substring(ruleStr, idxD, idxD+2).replaceAll('[^0-9]', '')}">1</span>
                </th:block>
            </span>
                                        <span th:unless="${#strings.contains(ruleStr, '&quot;deduct&quot;')}">1</span> point(s)
                                    </th:block>

                                </th:block>
                            </div>

                        </div>
                    </div>

                    <!-- AI Evaluation -->
                    <div th:if="${rule.aiScore != null}"
                         th:class="${rule.manualScore != null ? 'ai-evaluation' : 'normal-ai-evaluation'}"
                         style="font-family: 'Microsoft YaHei';">
                        <img th:if="${aiIconBase64 != null}"
                             th:src="'data:image/png;base64,' + ${aiIconBase64}"
                             alt="AI Icon"
                             style="width: 16px; height: 16px; vertical-align: middle; margin-right: 5px;"/>
                        <span th:unless="${aiIconBase64 != null}" class="ai-icon"></span>
                        Intelligent inspection completed
                        <b th:class="${rule.aiScore >= 0 ? 'add' : 'deduct'}"
                           th:text="${record.scoringRule == 1 ? 'add' : 'deduct'}"></b>
                        <b th:class="${rule.aiScore >= 0 ? 'addScore' : 'deductScore'}"
                           th:text="${(rule.aiScore >= 0 ? #numbers.formatInteger(rule.aiScore, 0) : #numbers.formatInteger(-rule.aiScore, 0)) + ' point(s)'}"></b>
                    </div>

                    <!-- Manual Evaluation -->
                    <div th:if="${rule.manualScore != null}" class="artificial-evaluation" style="font-family: 'Microsoft YaHei';">
                        <img th:if="${manualIconBase64 != null}"
                             th:src="'data:image/png;base64,' + ${manualIconBase64}"
                             alt="Manual Icon"
                             style="width: 16px; height: 16px; vertical-align: middle; margin-right: 5px;"/>
                        <span th:unless="${manualIconBase64 != null}" class="manual-icon"></span>
                        Manual evaluation
                        <b th:class="${rule.manualScore >= 0 ? 'add' : 'deduct'}"
                           th:text="${record.scoringRule == 1 ? 'add' : 'deduct'}"></b>
                        <b th:class="${rule.manualScore >= 0 ? 'addScore' : 'deductScore'}"
                           th:text="${(rule.manualScore >= 0 ? #numbers.formatInteger(rule.manualScore, 0) : #numbers.formatInteger(-rule.manualScore, 0)) + ' point(s)'}"></b>
                    </div>

                    <!-- Evaluation Standard Reference -->
                    <div th:if="${not #strings.isEmpty(rule.reference)}" class="rule-reference">
                        <div>Evaluation Standard Reference: </div>
                        <div th:text="${rule.reference}"></div>
                    </div>

                    <!-- Assessment Remarks -->
                    <div th:if="${not #strings.isEmpty(rule.assessmentRemark)}" class="rule-remark">
                        <div class="rule-point-title">Assessment Remarks: </div>
                        <div th:text="${rule.assessmentRemark}"></div>
                    </div>
                </div>

                <!-- Recursively process subcategories -->
                <div th:if="${not #lists.isEmpty(category.children)}"
                     th:insert="~{:: .categories}"
                     th:with="categories=${category.children}">
                </div>
            </div>
        </div>
    </div>
</div>

<div class="page end-page">
    <div class="content" style="text-align: center;">
        <img src="data:image/png;base64,${logoPngBase64}" alt="Logo" width="125" />
        <p th:text="'This is the complete content of this report, ' + ${totalPages} + ' pages in total'"></p>
        <p class="create-time" th:text="${record.createTimeStr} + ' Generated'"></p>
    </div>
</div>
</body>
</html>