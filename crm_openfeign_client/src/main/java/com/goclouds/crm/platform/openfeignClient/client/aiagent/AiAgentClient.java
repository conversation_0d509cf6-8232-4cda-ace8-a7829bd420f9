package com.goclouds.crm.platform.openfeignClient.client.aiagent;

import com.alibaba.fastjson.JSONObject;
import com.goclouds.crm.platform.openfeignClient.domain.R;
import com.goclouds.crm.platform.openfeignClient.domain.agent.ReminderSettingsVo;
import feign.Headers;
import feign.Request;
import feign.Response;
import feign.Retryer;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Flux;

import java.util.List;

@FeignClient(value = "crm-service-aiagent", contextId = "aiagent", path = "/")
public interface AiAgentClient {
    /**
     * @return
     */
    @PostMapping(value = "/run", produces = MediaType.TEXT_EVENT_STREAM_VALUE, headers = "Content-Type=application/json")
    @Headers({"Content-Type=application/json"})
    Response run(@RequestBody JSONObject aiAgent);


//    @PostMapping(value = "/run/test")
//    R<JSONObject> runTest(@RequestBody JSONObject aiAgent);


    /**
     * ai agent run test 流式
     *
     * @return
     */
    @PostMapping(value = "/run/test/stream", produces = "text/event-stream")
    Flux<String> runTestStream(@RequestBody JSONObject aiAgent);

    /**
     * ai agent run test 流式 内部调
     *
     * @return
     */
    @PostMapping(value = "/run/inner/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE, headers = "Content-Type=application/json")
    @Headers({"Content-Type=application/json"})
    Response runInnerStream(@RequestBody JSONObject aiAgent);


    /**
     * 创建公司生成默认智能体相关
     */
    @GetMapping("/create/defaultAndEnsure")
    R createDefaultAndEnsure(@RequestParam String companyId);


    /**
     * 查询意图提醒设置列表
     */
    @PostMapping("/reminder/inner/list")
    R<List<ReminderSettingsVo>> reminderList(@RequestBody ReminderSettingsVo settingsVo);

    /**
     * 查询智能体数量
     *
     * @param
     * @return 响应信息
     */
    @GetMapping("/queryAgentCount")
    R<Integer> queryAgentCount(@RequestParam String companyId);

//    class FeignConfig {
//        @Bean
//        public Request.Options options() {
//            // 连接超时时间设置为 5 秒，读取超时时间设置为 10 秒
//            return new Request.Options(50000, 100000);
//        }
//
//        @Bean
//        public Retryer retryer() {
//            // 初始间隔时间为 100 毫秒，最大间隔时间为 1 秒，最多重试 3 次
//            return new Retryer.Default(100, 1000, 3);
//        }
//    }

    /**
     * 获取智能体信息，根据绑定的系统手机号查询。
     *
     * @param companyId   公司ID。
     * @param systemPhone 系统手机号。
     * @return 智能体信息。- AiAgentInfoVo
     */
    @GetMapping("/getAgentInfoBySystemPhone")
    R<?> getAgentInfoBySystemPhone(@RequestParam String companyId, @RequestParam String systemPhone);

    /**
     * 获取智能体信息，根据智能体id。
     *
     * @param aiAgentId   智能体Id。
     * @return 智能体信息。- CrmAiAgentInfo
     */
    @GetMapping("/getAiAgentApiInfo")
    R<?> getAiAgentApiInfo(@RequestParam String aiAgentId);

}
