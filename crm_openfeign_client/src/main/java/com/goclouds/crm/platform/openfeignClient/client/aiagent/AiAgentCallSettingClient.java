package com.goclouds.crm.platform.openfeignClient.client.aiagent;

import com.goclouds.crm.platform.openfeignClient.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "crm-service-aiagent", contextId = "aiagent-call-setting-service", path = "/aiagent/call/setting")
public interface AiAgentCallSettingClient {

    /**
     * 获取通话设置详情
     * @param aiAgentId 智能体ID
     * @return 通话设置详情 - AiAgentCallSettingVo
     */
    @GetMapping("/detail")
    R<?> detail(@RequestParam String aiAgentId);
}
