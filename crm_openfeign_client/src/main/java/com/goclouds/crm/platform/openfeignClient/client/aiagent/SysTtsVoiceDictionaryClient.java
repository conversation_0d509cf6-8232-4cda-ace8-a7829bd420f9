package com.goclouds.crm.platform.openfeignClient.client.aiagent;

import com.goclouds.crm.platform.openfeignClient.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "crm-service-aiagent", contextId = "aiagent-stt-service", path = "/aiagent/tts/voice")
public interface SysTtsVoiceDictionaryClient {

    /**
     * 获取voiceCode获取信息。
     *
     * @param voiceCode 语音code
     * @return 语音信息 - SysTtsVoiceDictionary
     */
    @GetMapping("/voiceByVoiceCode")
    R<?> voiceByVoiceCode(@RequestParam String voiceCode);

}
