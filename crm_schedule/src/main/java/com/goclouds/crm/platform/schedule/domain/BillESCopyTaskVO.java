package com.goclouds.crm.platform.schedule.domain;

import cn.hutool.core.convert.Convert;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class BillESCopyTaskVO {

    /**
     * esid
     */
    private String esId;
    /**
     * 创建时间
     */
    private LocalDateTime currentTime;

    /**
     * 当前信息对应的年月 - 便于后续操作
     */
    private String year;
    private String month;


    public String getYear() {
        return Convert.toStr(this.getCurrentTime().getYear());
    }


    public String getMonth() {
        return Convert.toStr(this.getCurrentTime().getMonthValue());
    }


}
