package com.goclouds.crm.platform.schedule.domain;

import cn.hutool.core.convert.Convert;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WorkRecordESCopyTaskVO {

    /**
     * esid
     */
    private String esId;
    /**
     * 工单id
     */
    private String workRecordId;
    /**
     * 工单编号
     */
    private String wordRecordCode;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    /**
     * 当前信息对应的年月 - 便于后续操作
     */
    private String year;
    private String month;


    public String getYear() {
        return Convert.toStr(this.getCreateTime().getYear());
    }


    public String getMonth() {
        return Convert.toStr(this.getCreateTime().getMonthValue());
    }


}
